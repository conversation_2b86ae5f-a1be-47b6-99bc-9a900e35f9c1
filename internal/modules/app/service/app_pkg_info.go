package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	"gitlab.bingosoft.net/cloud-public/logger"
)

// ComponentType 组件类型
type ComponentType string

const (
	// CommonComponent 共性组件
	CommonComponent ComponentType = "common"
	// Infrastructure 基础存算底座
	Infrastructure ComponentType = "infrastructure"
	// Middleware 统一基础中间件
	Middleware ComponentType = "middleware"
	// Application 常规应用
	Application ComponentType = "app"
	// Builtin 内部组件默认安装
	Builtin ComponentType = "builtin"
)

// ParameterType 参数类型
type ParameterType string

const (
	// InputType 输入框类型
	InputType ParameterType = "input"
	// SelectType 下拉选择类型
	SelectType ParameterType = "select"
)

// ParameterOption 参数选项
type ParameterOption struct {
	// Value 参数值
	Value string `json:"value" yaml:"value"`
	// Label 参数标签
	Label string `json:"label" yaml:"label"`
}

// Parameter 组件参数
type Parameter struct {
	// Name 参数名称
	Name string `json:"name" yaml:"name"`
	// Type 参数类型
	Type ParameterType `json:"type" yaml:"type"`
	// DefaultValue 默认值
	DefaultValue string `json:"defaultValue" yaml:"defaultValue"`
	// Options 选项列表（type为select时必填）
	Options []ParameterOption `json:"options" yaml:"options"`
	// Description 参数描述
	Description string `json:"description" yaml:"description"`
	// Filled 是否已填充
	Filled bool `json:"filled" yaml:"filled"`
	// SrcDataType 数据源类型
	SrcDataType string `json:"srcDataType" yaml:"srcDataType"`
}

// Resources 资源预估
type Resources struct {
	// CPU CPU预估值（核）
	CPU float64 `json:"cpu" yaml:"cpu"`
	// Memory 内存预估值（GB）
	Memory float64 `json:"memory" yaml:"memory"`
	// Storage 存储预估值（GB）
	Storage float64 `json:"storage" yaml:"storage"`
}

// MetaInfo 组件元数据信息
type MetaInfo struct {
	// Component 组件名称
	Component string `json:"component" yaml:"component"`
	// DisplayName 显示名称
	DisplayName string `json:"displayName" yaml:"displayName"`
	// Version 组件版本
	Version string `json:"version" yaml:"version"`
	// Description 组件功能描述
	Description string `json:"description" yaml:"description"`
	// Vendor 厂商名称
	Vendor string `json:"vendor" yaml:"vendor"`
	// Type 组件类型
	Type ComponentType `json:"type" yaml:"type"`
	// Module 组件模块
	Module string `json:"module" yaml:"module"`
	// Namespace 命名空间
	Namespace string `json:"namespace" yaml:"namespace"`
	// HealthCheckTimeout 健康检查超时时间（秒）
	HealthCheckTimeout int64 `json:"healthCheckTimeout" yaml:"healthCheckTimeout"`
	// Dependencies 组件依赖列表
	Dependencies []string `json:"dependencies" yaml:"dependencies"`
	// Resources 资源预估
	Resources Resources `json:"resources" yaml:"resources"`
	// Parameters 组件参数列表
	Parameters []Parameter `json:"parameters" yaml:"parameters"`
}

// AppPackageInfo 应用包信息
type AppPackageInfo struct {
	// 应用名称
	Name string `json:"name" yaml:"name"`

	// 应用部署模式
	DeployMode constants.DeployModeType `json:"deployMode" yaml:"deploy-mode"`

	// 元数据信息
	Meta *MetaInfo `json:"meta" yaml:"meta"`

	// 基础目录
	BaseDir string
}

func (p *AppPackageInfo) GetHealthCheckTimeout() int64 {
	if p.Meta.HealthCheckTimeout == 0 {
		return constants.DefaultHealthCheckTimeout
	}
	return p.Meta.HealthCheckTimeout
}

// NewAppPackageInfo 创建应用包信息
func NewAppPackageInfo(app string, deployMode constants.DeployModeType, baseDir string) (*AppPackageInfo, error) {
	pkg := &AppPackageInfo{
		Name:       app,
		DeployMode: deployMode,
		BaseDir:    baseDir,
	}
	if err := pkg.LoadMeta(); err != nil {
		return nil, err
	}
	return pkg, nil
}

// GetComponentPath 获取组件路径
func (p *AppPackageInfo) GetComponentPath() string {
	// 根据部署模式拼接基础目录路径
	baseVendorDir := filepath.Join(p.BaseDir, constants.VendorDirName)

	// 获取vendors下所有厂商目录
	vendors, err := filepath.Glob(filepath.Join(baseVendorDir, "*"))
	if err != nil {
		return ""
	}

	// 遍历所有厂商目录
	for _, vendor := range vendors {
		// 检查厂商目录下的components目录
		componentsDir := filepath.Join(vendor, constants.ComponentsDirName)
		// 检查components目录中是否有与应用名匹配的目录
		appDir := filepath.Join(componentsDir, p.Name)
		if _, err := os.Stat(appDir); err == nil {
			// 找到了应用目录
			return filepath.Join(appDir, string(p.DeployMode))
		}
	}

	// 如果找不到应用，返回空字符串
	return ""
}

// GetMetaPath 获取meta.yaml文件路径
func (p *AppPackageInfo) GetMetaPath() string {
	return filepath.Join(p.GetComponentPath(), "meta.yaml")
}

// LoadMeta 加载meta.yaml文件
func (p *AppPackageInfo) LoadMeta() error {
	metaPath := p.GetMetaPath()
	data, err := os.ReadFile(metaPath)
	if err != nil {
		return fmt.Errorf("failed to read meta file %s: %w", metaPath, err)
	}

	p.Meta = &MetaInfo{}
	return yaml.Unmarshal(data, p.Meta)
}

// GetNamespace 获取应用命名空间
func (p *AppPackageInfo) GetNamespace() string {
	if p.Meta.Namespace == "" {
		return "default"
	}

	return p.Meta.Namespace
}

// GetChartPath 获取Helm Chart路径
func (p *AppPackageInfo) GetChartPath() string {
	// 根据开发手册中的规范，Chart包格式为：组件名-版本号.tgz
	// 由于版本号可能不确定，这里使用通配符匹配
	matches, _ := filepath.Glob(filepath.Join(p.GetComponentPath(), "*.tgz"))
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

// GetDockerComposePath 获取docker-compose.yaml路径
func (p *AppPackageInfo) GetDockerComposePath() string {
	return filepath.Join(p.GetComponentPath(), constants.DockerComposeFileName)
}

// GetNodeInitScriptPath 获取组件节点初始化脚本路径
func (p *AppPackageInfo) GetNodeInitScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.NodeInitScriptFileName)
}

// GetPreInstallScriptPath 获取安装前脚本路径
func (p *AppPackageInfo) GetPreInstallScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.PreInstallScriptFileName)
}

// GetHealthCheckScriptPath 获取健康检查脚本路径
func (p *AppPackageInfo) GetHealthCheckScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.HealthCheckScriptFileName)
}

// GetUninstallScriptPath 获取卸载脚本路径
func (p *AppPackageInfo) GetUninstallScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.UninstallScriptFileName)
}

// GetStartScriptPath 获取启动脚本路径
func (p *AppPackageInfo) GetStartScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.StartScriptFileName)
}

// GetResetScriptPath 获取重置脚本路径
func (p *AppPackageInfo) GetResetScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.ResetScriptFileName)
}

// GetStopScriptPath 获取停止脚本路径
func (p *AppPackageInfo) GetStopScriptPath() string {
	return filepath.Join(p.GetComponentPath(), constants.ScriptsDirName, constants.StopScriptFileName)
}

// GetAllComponentsInfo 获取所有组件的信息
// deployMode 指定部署模式，用于获取对应模式下的组件信息
func GetAllComponentsInfo(ctx context.Context, deployMode constants.DeployModeType, baseDir string) ([]MetaInfo, error) {
	// 获取基础目录路径
	baseVendorDir := filepath.Join(baseDir, constants.VendorDirName)

	// 获取所有厂商目录
	vendors, err := filepath.Glob(filepath.Join(baseVendorDir, "*"))
	if err != nil {
		logger.Errorf(ctx, "Failed to get vendor directories: %v", err)
		return nil, err
	}

	var allComponents []MetaInfo

	// 遍历所有厂商目录
	for _, vendor := range vendors {
		// 检查厂商目录下的components目录
		componentsDir := filepath.Join(vendor, constants.ComponentsDirName)
		components, err := filepath.Glob(filepath.Join(componentsDir, "*"))
		if err != nil {
			logger.Warnf(ctx, "Failed to get components in vendor directory %s: %v", vendor, err)
			continue
		}

		// 遍历所有组件目录
		for _, component := range components {
			// 获取组件名称
			componentName := filepath.Base(component)

			// 创建临时AppPackageInfo对象用于读取meta信息
			appInfo, err := NewAppPackageInfo(componentName, deployMode, baseDir)
			if err != nil {
				logger.Warnf(ctx, "Failed to create AppPackageInfo for component %s: %v", componentName, err)
				continue
			}

			// 将组件信息添加到结果列表
			if appInfo.Meta != nil {
				allComponents = append(allComponents, *appInfo.Meta)
			} else {
				logger.Warnf(ctx, "Meta info is nil for component %s", componentName)
			}
		}
	}

	return allComponents, nil
}

// HasHealthCheckScript 检查健康检查脚本是否存在
func (p *AppPackageInfo) HasHealthCheckScript() bool {
	scriptPath := p.GetHealthCheckScriptPath()
	if scriptPath == "" {
		return false
	}

	_, err := os.Stat(scriptPath)
	return err == nil
}

// HasStartScript 检查启动脚本是否存在
func (p *AppPackageInfo) HasStartScript() bool {
	scriptPath := p.GetStartScriptPath()
	if scriptPath == "" {
		return false
	}

	_, err := os.Stat(scriptPath)
	return err == nil
}

// HasResetScript 检查重置脚本是否存在
func (p *AppPackageInfo) HasResetScript() bool {
	scriptPath := p.GetStartScriptPath()
	if scriptPath == "" {
		return false
	}

	_, err := os.Stat(scriptPath)
	return err == nil
}

// HasStopScript 检查停止脚本是否存在
func (p *AppPackageInfo) HasStopScript() bool {
	scriptPath := p.GetStopScriptPath()
	if scriptPath == "" {
		return false
	}

	_, err := os.Stat(scriptPath)
	return err == nil
}

// HasUninstallScript 检查卸载脚本是否存在
func (p *AppPackageInfo) HasUninstallScript() bool {
	scriptPath := p.GetUninstallScriptPath()
	if scriptPath == "" {
		return false
	}

	_, err := os.Stat(scriptPath)
	return err == nil
}
